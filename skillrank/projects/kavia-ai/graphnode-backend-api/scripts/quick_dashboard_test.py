#!/usr/bin/env python3
"""
Quick Dashboard Test Script
Simple script to quickly test dashboard endpoint performance
"""

import requests
import time
import json
import sys
from datetime import datetime

class QuickDashboardTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 60  # 60 second timeout
        
    def test_endpoint(self, endpoint, params=None):
        """Test a single endpoint and return timing/results"""
        url = f"{self.base_url}{endpoint}"
        
        print(f"🧪 Testing: {endpoint}")
        print(f"   URL: {url}")
        if params:
            print(f"   Params: {params}")
        
        start_time = time.time()
        
        try:
            response = self.session.get(url, params=params)
            end_time = time.time()
            execution_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                data_size = len(response.content)
                
                print(f"✅ SUCCESS: {execution_time:.2f}s")
                print(f"   Status: {response.status_code}")
                print(f"   Response size: {data_size:,} bytes")
                
                # Performance assessment
                if execution_time < 2.0:
                    print(f"   Performance: 🟢 EXCELLENT (< 2s)")
                elif execution_time < 5.0:
                    print(f"   Performance: 🟡 GOOD (2-5s)")
                elif execution_time < 10.0:
                    print(f"   Performance: 🟠 ACCEPTABLE (5-10s)")
                else:
                    print(f"   Performance: 🔴 POOR (> 10s)")
                
                return {
                    'success': True,
                    'execution_time': execution_time,
                    'status_code': response.status_code,
                    'data_size': data_size,
                    'data': data
                }
            else:
                print(f"❌ ERROR: HTTP {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                return {
                    'success': False,
                    'execution_time': execution_time,
                    'status_code': response.status_code,
                    'error': response.text
                }
                
        except requests.exceptions.Timeout:
            end_time = time.time()
            execution_time = end_time - start_time
            print(f"⏰ TIMEOUT after {execution_time:.2f}s")
            return {
                'success': False,
                'execution_time': execution_time,
                'error': 'Timeout'
            }
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            print(f"💥 EXCEPTION: {str(e)}")
            return {
                'success': False,
                'execution_time': execution_time,
                'error': str(e)
            }
    
    def test_dashboard_overview(self, tenant_id="b2c", days=7):
        """Test dashboard overview endpoint"""
        endpoint = f"/dashboard/overview/{tenant_id}"
        params = {'days': days}
        
        result = self.test_endpoint(endpoint, params)
        
        if result['success']:
            data = result['data']
            summary = data.get('summary', {})
            
            print(f"📊 Data Summary:")
            print(f"   Total requests: {summary.get('total_requests', 0):,}")
            print(f"   Unique users: {summary.get('unique_users', 0):,}")
            print(f"   Avg response time: {summary.get('avg_response_time', 0):.2f}ms")
            print(f"   Error rate: {summary.get('error_rate', 0):.2f}%")
            print(f"   Daily stats entries: {len(data.get('daily_stats', []))}")
            print(f"   Top endpoints: {len(data.get('top_endpoints', []))}")
            print(f"   Geographic data points: {len(data.get('geographic_data', []))}")
        
        return result
    
    def test_geographic_data(self, tenant_id="b2c", days=7):
        """Test geographic data endpoint"""
        endpoint = f"/dashboard/geographic/{tenant_id}"
        params = {'days': days}
        
        result = self.test_endpoint(endpoint, params)
        
        if result['success']:
            data = result['data']
            geo_data = data.get('geographic_data', [])
            
            print(f"🌍 Geographic Data:")
            print(f"   Data points: {len(geo_data)}")
            if geo_data:
                print(f"   Top location: {geo_data[0].get('_id', {})}")
        
        return result
    
    def test_tenants_list(self):
        """Test tenants list endpoint"""
        endpoint = "/dashboard/tenants"
        
        result = self.test_endpoint(endpoint)
        
        if result['success']:
            data = result['data']
            tenants = data.get('tenants', [])
            
            print(f"👥 Tenants:")
            print(f"   Count: {len(tenants)}")
            if tenants:
                tenant_ids = [t.get('id', 'unknown') for t in tenants[:5]]
                print(f"   Sample IDs: {', '.join(tenant_ids)}")
        
        return result
    
    def test_cache_effectiveness(self, tenant_id="b2c", days=7):
        """Test cache by making the same request twice"""
        print(f"🔄 Testing cache effectiveness...")
        
        # First request
        print("   First request (should hit database):")
        result1 = self.test_dashboard_overview(tenant_id, days)
        
        if not result1['success']:
            print("   ❌ First request failed, skipping cache test")
            return {'cache_working': False, 'error': 'First request failed'}
        
        time.sleep(1)  # Small delay
        
        # Second request
        print("   Second request (should hit cache):")
        result2 = self.test_dashboard_overview(tenant_id, days)
        
        if not result2['success']:
            print("   ❌ Second request failed")
            return {'cache_working': False, 'error': 'Second request failed'}
        
        # Compare times
        time1 = result1['execution_time']
        time2 = result2['execution_time']
        speedup = time1 / time2 if time2 > 0 else 1
        
        print(f"🔄 Cache Results:")
        print(f"   First request: {time1:.2f}s")
        print(f"   Second request: {time2:.2f}s")
        print(f"   Speedup: {speedup:.2f}x")
        
        if speedup > 1.5:
            print(f"   ✅ Cache is working effectively!")
            cache_working = True
        else:
            print(f"   ⚠️  Cache may not be working optimally")
            cache_working = False
        
        return {
            'cache_working': cache_working,
            'first_time': time1,
            'second_time': time2,
            'speedup': speedup
        }
    
    def run_quick_test(self, tenant_id="b2c"):
        """Run a quick comprehensive test"""
        print("🚀 QUICK DASHBOARD TEST")
        print("=" * 50)
        print(f"API URL: {self.base_url}")
        print(f"Tenant ID: {tenant_id}")
        print(f"Timestamp: {datetime.now()}")
        print()
        
        results = {}
        
        # Test 1: Tenants list
        print("📋 Test 1: Tenants List")
        results['tenants'] = self.test_tenants_list()
        print()
        
        # Test 2: Dashboard overview (different time ranges)
        time_ranges = [1, 7, 30]
        results['dashboard'] = {}
        
        for days in time_ranges:
            print(f"📊 Test 2.{days}: Dashboard Overview ({days} days)")
            results['dashboard'][f'{days}_days'] = self.test_dashboard_overview(tenant_id, days)
            print()
        
        # Test 3: Geographic data
        print("🌍 Test 3: Geographic Data")
        results['geographic'] = self.test_geographic_data(tenant_id, 7)
        print()
        
        # Test 4: Cache effectiveness
        print("🔄 Test 4: Cache Effectiveness")
        results['cache'] = self.test_cache_effectiveness(tenant_id, 7)
        print()
        
        # Summary
        self.print_summary(results)
        
        return results
    
    def print_summary(self, results):
        """Print test summary"""
        print("📋 TEST SUMMARY")
        print("=" * 50)
        
        total_tests = 0
        successful_tests = 0
        total_time = 0
        
        # Count results
        for test_name, test_result in results.items():
            if test_name == 'cache':
                continue  # Skip cache test in counting
            elif test_name == 'dashboard':
                for sub_test, sub_result in test_result.items():
                    total_tests += 1
                    if sub_result['success']:
                        successful_tests += 1
                        total_time += sub_result['execution_time']
            else:
                total_tests += 1
                if test_result['success']:
                    successful_tests += 1
                    total_time += test_result['execution_time']
        
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        avg_time = total_time / successful_tests if successful_tests > 0 else 0
        
        print(f"✅ Success Rate: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        print(f"⏱️  Average Response Time: {avg_time:.2f}s")
        
        # Cache effectiveness
        cache_result = results.get('cache', {})
        if cache_result.get('cache_working'):
            speedup = cache_result.get('speedup', 1)
            print(f"🔄 Cache: ✅ Working ({speedup:.2f}x speedup)")
        else:
            print(f"🔄 Cache: ⚠️  Not optimal")
        
        # Overall assessment
        if success_rate == 100 and avg_time < 5:
            print("🎉 Overall: EXCELLENT - Dashboard is performing very well!")
        elif success_rate >= 80 and avg_time < 10:
            print("👍 Overall: GOOD - Dashboard is performing well")
        elif success_rate >= 60:
            print("⚠️  Overall: ACCEPTABLE - Some issues detected")
        else:
            print("🔴 Overall: POOR - Significant issues detected")
        
        print("=" * 50)

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Quick Dashboard Test')
    parser.add_argument('--url', default='http://localhost:8000', help='API base URL')
    parser.add_argument('--tenant', default='b2c', help='Tenant ID to test')
    
    args = parser.parse_args()
    
    tester = QuickDashboardTester(args.url)
    
    try:
        results = tester.run_quick_test(args.tenant)
        
        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"quick_test_results_{timestamp}.json"
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"📄 Results saved to: {output_file}")
        
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
