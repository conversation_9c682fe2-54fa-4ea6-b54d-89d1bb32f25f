#!/bin/bash

# Dashboard Testing Suite Runner
# This script runs all dashboard tests in sequence

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
API_URL="http://localhost:8000"
TENANT_ID="b2c"
SKIP_OPTIMIZATION=false
QUICK_ONLY=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if script exists
check_script() {
    if [ ! -f "$1" ]; then
        print_error "Script not found: $1"
        return 1
    fi
    return 0
}

# Function to run a Python script with error handling
run_python_script() {
    local script_name="$1"
    local description="$2"
    shift 2
    local args="$@"
    
    print_status "Running $description..."
    
    if ! check_script "$script_name"; then
        return 1
    fi
    
    if python3 "$script_name" $args; then
        print_success "$description completed successfully"
        return 0
    else
        print_error "$description failed"
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "Dashboard Testing Suite"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --url URL          API base URL (default: http://localhost:8000)"
    echo "  --tenant TENANT    Tenant ID to test (default: b2c)"
    echo "  --skip-optimization Skip database optimization steps"
    echo "  --quick-only       Run only quick tests"
    echo "  --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run all tests with defaults"
    echo "  $0 --url http://api.example.com      # Test against different API"
    echo "  $0 --tenant my-tenant --quick-only   # Quick test for specific tenant"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --url)
            API_URL="$2"
            shift 2
            ;;
        --tenant)
            TENANT_ID="$2"
            shift 2
            ;;
        --skip-optimization)
            SKIP_OPTIMIZATION=true
            shift
            ;;
        --quick-only)
            QUICK_ONLY=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    echo "🚀 DASHBOARD TESTING SUITE"
    echo "=========================="
    echo "API URL: $API_URL"
    echo "Tenant ID: $TENANT_ID"
    echo "Quick only: $QUICK_ONLY"
    echo "Skip optimization: $SKIP_OPTIMIZATION"
    echo ""
    
    # Change to script directory
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    cd "$SCRIPT_DIR"
    
    # Step 1: Database optimization (unless skipped)
    if [ "$SKIP_OPTIMIZATION" = false ]; then
        print_status "Step 1: Database Optimization"
        echo "----------------------------------------"
        
        # Create indexes
        if run_python_script "create_dashboard_indexes.py" "Database index creation"; then
            print_success "Database indexes created/verified"
        else
            print_warning "Index creation failed, continuing with tests..."
        fi
        
        echo ""
    else
        print_warning "Skipping database optimization as requested"
        echo ""
    fi
    
    # Step 2: Performance monitoring
    print_status "Step 2: Performance Monitoring"
    echo "----------------------------------------"
    
    if run_python_script "monitor_dashboard_performance.py" "Performance monitoring" "$TENANT_ID"; then
        print_success "Performance monitoring completed"
    else
        print_warning "Performance monitoring failed, continuing with tests..."
    fi
    
    echo ""
    
    # Step 3: Quick functional test
    print_status "Step 3: Quick Functional Test"
    echo "----------------------------------------"
    
    if run_python_script "quick_dashboard_test.py" "Quick functional test" "--url $API_URL --tenant $TENANT_ID"; then
        print_success "Quick functional test passed"
    else
        print_error "Quick functional test failed"
        if [ "$QUICK_ONLY" = true ]; then
            print_error "Stopping here due to --quick-only flag"
            exit 1
        fi
        print_warning "Continuing with remaining tests..."
    fi
    
    echo ""
    
    # If quick-only, stop here
    if [ "$QUICK_ONLY" = true ]; then
        print_success "Quick tests completed successfully!"
        exit 0
    fi
    
    # Step 4: Comprehensive endpoint testing
    print_status "Step 4: Comprehensive Endpoint Testing"
    echo "----------------------------------------"
    
    if run_python_script "test_dashboard_endpoint.py" "Comprehensive endpoint testing" "--url $API_URL --tenant $TENANT_ID"; then
        print_success "Comprehensive endpoint testing completed"
    else
        print_warning "Comprehensive endpoint testing had issues, continuing..."
    fi
    
    echo ""
    
    # Step 5: Load testing
    print_status "Step 5: Load Testing"
    echo "----------------------------------------"
    
    if run_python_script "dashboard_load_test.py" "Load testing" "--url $API_URL --tenant $TENANT_ID"; then
        print_success "Load testing completed"
    else
        print_warning "Load testing had issues"
    fi
    
    echo ""
    
    # Final summary
    print_status "Test Suite Summary"
    echo "----------------------------------------"
    
    # Check if any result files were created
    RESULT_FILES=$(find . -name "*test_results_*.json" -o -name "*performance_report_*.json" -o -name "*load_test_results_*.json" 2>/dev/null | head -5)
    
    if [ -n "$RESULT_FILES" ]; then
        print_success "Test results saved to:"
        echo "$RESULT_FILES" | while read -r file; do
            echo "  📄 $file"
        done
    fi
    
    echo ""
    print_success "🎉 Dashboard testing suite completed!"
    echo ""
    echo "Next steps:"
    echo "1. Review the generated JSON reports for detailed results"
    echo "2. Check server logs for any errors during testing"
    echo "3. If performance issues were found, run the optimization scripts"
    echo "4. Consider setting up monitoring for production environments"
    echo ""
}

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is required but not installed"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "create_dashboard_indexes.py" ]; then
    print_error "Please run this script from the scripts directory"
    print_error "Current directory: $(pwd)"
    exit 1
fi

# Run main function
main "$@"
