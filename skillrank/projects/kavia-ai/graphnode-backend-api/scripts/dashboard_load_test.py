#!/usr/bin/env python3
"""
Dashboard Load Testing Script
Stress test the dashboard endpoints with concurrent requests
"""

import asyncio
import aiohttp
import time
import statistics
import json
from datetime import datetime
import sys

class DashboardLoadTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.results = []
        
    async def make_request(self, session, endpoint, params=None, request_id=None):
        """Make a single request and record timing"""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            async with session.get(url, params=params) as response:
                end_time = time.time()
                execution_time = end_time - start_time
                
                if response.status == 200:
                    data = await response.json()
                    return {
                        'request_id': request_id,
                        'success': True,
                        'execution_time': execution_time,
                        'status_code': response.status,
                        'response_size': len(await response.read()),
                        'data_points': self._count_data_points(data)
                    }
                else:
                    return {
                        'request_id': request_id,
                        'success': False,
                        'execution_time': execution_time,
                        'status_code': response.status,
                        'error': f"HTTP {response.status}"
                    }
                    
        except asyncio.TimeoutError:
            end_time = time.time()
            return {
                'request_id': request_id,
                'success': False,
                'execution_time': end_time - start_time,
                'error': 'Timeout'
            }
        except Exception as e:
            end_time = time.time()
            return {
                'request_id': request_id,
                'success': False,
                'execution_time': end_time - start_time,
                'error': str(e)
            }
    
    def _count_data_points(self, data):
        """Count data points in response"""
        if not isinstance(data, dict):
            return 0
            
        count = 0
        count += len(data.get('daily_stats', []))
        count += len(data.get('hourly_trends', []))
        count += len(data.get('top_endpoints', []))
        count += len(data.get('top_users', []))
        count += len(data.get('geographic_data', []))
        
        return count
    
    async def run_concurrent_test(self, endpoint, params, concurrent_requests, duration_seconds=None, max_requests=None):
        """Run concurrent requests against an endpoint"""
        print(f"🚀 Load testing: {endpoint}")
        print(f"   Concurrent requests: {concurrent_requests}")
        if duration_seconds:
            print(f"   Duration: {duration_seconds}s")
        if max_requests:
            print(f"   Max requests: {max_requests}")
        
        # Setup session with connection pooling
        connector = aiohttp.TCPConnector(
            limit=concurrent_requests * 2,
            limit_per_host=concurrent_requests,
            keepalive_timeout=30
        )
        
        timeout = aiohttp.ClientTimeout(total=60)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            start_time = time.time()
            request_count = 0
            tasks = []
            results = []
            
            # Create semaphore to limit concurrent requests
            semaphore = asyncio.Semaphore(concurrent_requests)
            
            async def limited_request(request_id):
                async with semaphore:
                    return await self.make_request(session, endpoint, params, request_id)
            
            # Generate requests
            while True:
                current_time = time.time()
                
                # Check stopping conditions
                if duration_seconds and (current_time - start_time) >= duration_seconds:
                    break
                if max_requests and request_count >= max_requests:
                    break
                
                # Create batch of requests
                batch_size = min(concurrent_requests, (max_requests - request_count) if max_requests else concurrent_requests)
                
                for i in range(batch_size):
                    task = limited_request(f"{request_count + i + 1}")
                    tasks.append(task)
                    request_count += 1
                
                # Wait for batch to complete
                if len(tasks) >= concurrent_requests or (max_requests and request_count >= max_requests):
                    batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    for result in batch_results:
                        if isinstance(result, Exception):
                            results.append({
                                'success': False,
                                'error': str(result),
                                'execution_time': 0
                            })
                        else:
                            results.append(result)
                    
                    tasks = []
                
                # Small delay to prevent overwhelming
                if not max_requests:  # Only delay in duration-based tests
                    await asyncio.sleep(0.1)
            
            # Wait for remaining tasks
            if tasks:
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                for result in batch_results:
                    if isinstance(result, Exception):
                        results.append({
                            'success': False,
                            'error': str(result),
                            'execution_time': 0
                        })
                    else:
                        results.append(result)
            
            total_time = time.time() - start_time
            
            return self._analyze_results(results, total_time, concurrent_requests)
    
    def _analyze_results(self, results, total_time, concurrent_requests):
        """Analyze load test results"""
        total_requests = len(results)
        successful = sum(1 for r in results if r.get('success', False))
        failed = total_requests - successful
        
        # Calculate timing statistics
        success_times = [r['execution_time'] for r in results if r.get('success', False)]
        
        analysis = {
            'total_requests': total_requests,
            'successful_requests': successful,
            'failed_requests': failed,
            'success_rate': (successful / total_requests * 100) if total_requests > 0 else 0,
            'total_duration': total_time,
            'requests_per_second': total_requests / total_time if total_time > 0 else 0,
            'concurrent_requests': concurrent_requests
        }
        
        if success_times:
            analysis.update({
                'avg_response_time': statistics.mean(success_times),
                'min_response_time': min(success_times),
                'max_response_time': max(success_times),
                'median_response_time': statistics.median(success_times),
                'p95_response_time': self._percentile(success_times, 95),
                'p99_response_time': self._percentile(success_times, 99)
            })
        
        # Error analysis
        error_types = {}
        for result in results:
            if not result.get('success', False):
                error = result.get('error', 'Unknown')
                error_types[error] = error_types.get(error, 0) + 1
        
        analysis['error_breakdown'] = error_types
        
        return analysis
    
    def _percentile(self, data, percentile):
        """Calculate percentile"""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def print_results(self, results, test_name):
        """Print formatted results"""
        print(f"\n📊 {test_name} Results:")
        print("-" * 40)
        print(f"Total requests: {results['total_requests']:,}")
        print(f"Successful: {results['successful_requests']:,}")
        print(f"Failed: {results['failed_requests']:,}")
        print(f"Success rate: {results['success_rate']:.1f}%")
        print(f"Duration: {results['total_duration']:.2f}s")
        print(f"Requests/sec: {results['requests_per_second']:.2f}")
        
        if 'avg_response_time' in results:
            print(f"\nResponse Times:")
            print(f"  Average: {results['avg_response_time']:.3f}s")
            print(f"  Median: {results['median_response_time']:.3f}s")
            print(f"  Min: {results['min_response_time']:.3f}s")
            print(f"  Max: {results['max_response_time']:.3f}s")
            print(f"  95th percentile: {results['p95_response_time']:.3f}s")
            print(f"  99th percentile: {results['p99_response_time']:.3f}s")
        
        if results['error_breakdown']:
            print(f"\nError Breakdown:")
            for error, count in results['error_breakdown'].items():
                print(f"  {error}: {count}")
        
        # Performance assessment
        avg_time = results.get('avg_response_time', float('inf'))
        success_rate = results['success_rate']
        
        if success_rate >= 99 and avg_time < 2:
            print("🟢 Performance: EXCELLENT")
        elif success_rate >= 95 and avg_time < 5:
            print("🟡 Performance: GOOD")
        elif success_rate >= 90 and avg_time < 10:
            print("🟠 Performance: ACCEPTABLE")
        else:
            print("🔴 Performance: POOR")
    
    async def run_load_test_suite(self, tenant_id="b2c"):
        """Run comprehensive load test suite"""
        print("🚀 DASHBOARD LOAD TEST SUITE")
        print("=" * 50)
        print(f"Target: {self.base_url}")
        print(f"Tenant: {tenant_id}")
        print(f"Start time: {datetime.now()}")
        print()
        
        test_results = {}
        
        # Test scenarios
        scenarios = [
            {
                'name': 'Light Load',
                'endpoint': f'/dashboard/overview/{tenant_id}',
                'params': {'days': 7},
                'concurrent': 3,
                'max_requests': 20
            },
            {
                'name': 'Medium Load',
                'endpoint': f'/dashboard/overview/{tenant_id}',
                'params': {'days': 7},
                'concurrent': 5,
                'max_requests': 30
            },
            {
                'name': 'Heavy Load',
                'endpoint': f'/dashboard/overview/{tenant_id}',
                'params': {'days': 7},
                'concurrent': 10,
                'max_requests': 50
            },
            {
                'name': 'Geographic Data Load',
                'endpoint': f'/dashboard/geographic/{tenant_id}',
                'params': {'days': 7},
                'concurrent': 8,
                'max_requests': 40
            },
            {
                'name': 'Mixed Time Ranges',
                'endpoint': f'/dashboard/overview/{tenant_id}',
                'params': {'days': 30},
                'concurrent': 6,
                'max_requests': 25
            }
        ]
        
        for scenario in scenarios:
            print(f"🧪 Running: {scenario['name']}")
            
            results = await self.run_concurrent_test(
                scenario['endpoint'],
                scenario['params'],
                scenario['concurrent'],
                max_requests=scenario['max_requests']
            )
            
            test_results[scenario['name']] = results
            self.print_results(results, scenario['name'])
            
            # Cool down between tests
            print("⏸️  Cooling down...")
            await asyncio.sleep(3)
        
        # Overall summary
        self.print_overall_summary(test_results)
        
        return test_results
    
    def print_overall_summary(self, all_results):
        """Print overall test summary"""
        print("\n📋 OVERALL SUMMARY")
        print("=" * 50)
        
        total_requests = sum(r['total_requests'] for r in all_results.values())
        total_successful = sum(r['successful_requests'] for r in all_results.values())
        overall_success_rate = (total_successful / total_requests * 100) if total_requests > 0 else 0
        
        print(f"Total requests across all tests: {total_requests:,}")
        print(f"Overall success rate: {overall_success_rate:.1f}%")
        
        # Best and worst performing tests
        best_test = min(all_results.items(), key=lambda x: x[1].get('avg_response_time', float('inf')))
        worst_test = max(all_results.items(), key=lambda x: x[1].get('avg_response_time', 0))
        
        print(f"\nBest performing: {best_test[0]} ({best_test[1].get('avg_response_time', 0):.3f}s avg)")
        print(f"Worst performing: {worst_test[0]} ({worst_test[1].get('avg_response_time', 0):.3f}s avg)")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        if overall_success_rate < 95:
            print("   - Success rate is below 95%, investigate error causes")
        if any(r.get('avg_response_time', 0) > 5 for r in all_results.values()):
            print("   - Some tests show slow response times, consider optimization")
        if any(r.get('p99_response_time', 0) > 10 for r in all_results.values()):
            print("   - High 99th percentile times detected, check for outliers")
        
        if overall_success_rate >= 99 and all(r.get('avg_response_time', 0) < 3 for r in all_results.values()):
            print("   🎉 Excellent performance across all tests!")

async def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Dashboard Load Testing')
    parser.add_argument('--url', default='http://localhost:8000', help='API base URL')
    parser.add_argument('--tenant', default='b2c', help='Tenant ID to test')
    parser.add_argument('--concurrent', type=int, help='Number of concurrent requests')
    parser.add_argument('--requests', type=int, help='Total number of requests')
    parser.add_argument('--duration', type=int, help='Test duration in seconds')
    
    args = parser.parse_args()
    
    tester = DashboardLoadTester(args.url)
    
    try:
        if args.concurrent and (args.requests or args.duration):
            # Custom single test
            endpoint = f'/dashboard/overview/{args.tenant}'
            params = {'days': 7}
            
            results = await tester.run_concurrent_test(
                endpoint, params, args.concurrent,
                duration_seconds=args.duration,
                max_requests=args.requests
            )
            
            tester.print_results(results, "Custom Load Test")
        else:
            # Full test suite
            results = await tester.run_load_test_suite(args.tenant)
            
            # Save results
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"load_test_results_{timestamp}.json"
            
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            print(f"\n📄 Results saved to: {output_file}")
            
    except KeyboardInterrupt:
        print("\n⏹️  Load test interrupted by user")
    except Exception as e:
        print(f"\n❌ Load test failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
