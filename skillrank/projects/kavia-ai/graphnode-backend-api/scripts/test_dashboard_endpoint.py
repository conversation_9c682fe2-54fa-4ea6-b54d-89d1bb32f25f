#!/usr/bin/env python3
"""
Dashboard Endpoint Testing Script
This script tests the dashboard API endpoints with various scenarios
"""

import asyncio
import aiohttp
import time
import json
import logging
from datetime import datetime, timezone
import sys
import os
from typing import Dict, List
import statistics

# Add the parent directory to the path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.Settings import settings

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DashboardEndpointTester:
    def __init__(self, base_url: str = None, auth_token: str = None):
        self.base_url = base_url or "http://localhost:8000"  # Adjust to your API URL
        self.auth_token = auth_token
        self.session = None
        self.test_results = []
        
    async def __aenter__(self):
        """Async context manager entry"""
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=30)
        timeout = aiohttp.ClientTimeout(total=120)  # 2 minute timeout
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        if self.auth_token:
            headers['Authorization'] = f'Bearer {self.auth_token}'
            
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=headers
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def test_dashboard_overview(self, tenant_id: str, days: int = 7) -> Dict:
        """Test dashboard overview endpoint"""
        url = f"{self.base_url}/dashboard/overview/{tenant_id}"
        params = {'days': days}
        
        logger.info(f"🧪 Testing dashboard overview: {tenant_id} ({days} days)")
        
        start_time = time.time()
        
        try:
            async with self.session.get(url, params=params) as response:
                end_time = time.time()
                execution_time = end_time - start_time
                
                if response.status == 200:
                    data = await response.json()
                    
                    # Validate response structure
                    validation_result = self._validate_dashboard_response(data)
                    
                    result = {
                        'endpoint': 'dashboard_overview',
                        'tenant_id': tenant_id,
                        'days': days,
                        'status': 'SUCCESS',
                        'status_code': response.status,
                        'execution_time': execution_time,
                        'response_size': len(json.dumps(data)),
                        'validation': validation_result,
                        'data_summary': {
                            'total_requests': data.get('summary', {}).get('total_requests', 0),
                            'unique_users': data.get('summary', {}).get('unique_users', 0),
                            'daily_stats_count': len(data.get('daily_stats', [])),
                            'hourly_trends_count': len(data.get('hourly_trends', [])),
                            'top_endpoints_count': len(data.get('top_endpoints', [])),
                            'top_users_count': len(data.get('top_users', [])),
                            'geographic_data_count': len(data.get('geographic_data', []))
                        }
                    }
                    
                    # Performance assessment
                    if execution_time < 2.0:
                        result['performance'] = 'EXCELLENT'
                        logger.info(f"✅ {tenant_id} ({days}d): {execution_time:.2f}s - EXCELLENT")
                    elif execution_time < 5.0:
                        result['performance'] = 'GOOD'
                        logger.info(f"✅ {tenant_id} ({days}d): {execution_time:.2f}s - GOOD")
                    elif execution_time < 10.0:
                        result['performance'] = 'ACCEPTABLE'
                        logger.info(f"⚠️  {tenant_id} ({days}d): {execution_time:.2f}s - ACCEPTABLE")
                    else:
                        result['performance'] = 'POOR'
                        logger.info(f"🔴 {tenant_id} ({days}d): {execution_time:.2f}s - POOR")
                        
                else:
                    error_text = await response.text()
                    result = {
                        'endpoint': 'dashboard_overview',
                        'tenant_id': tenant_id,
                        'days': days,
                        'status': 'ERROR',
                        'status_code': response.status,
                        'execution_time': execution_time,
                        'error': error_text,
                        'performance': 'FAILED'
                    }
                    logger.error(f"❌ {tenant_id} ({days}d): HTTP {response.status} - {error_text}")
                    
        except asyncio.TimeoutError:
            end_time = time.time()
            execution_time = end_time - start_time
            result = {
                'endpoint': 'dashboard_overview',
                'tenant_id': tenant_id,
                'days': days,
                'status': 'TIMEOUT',
                'execution_time': execution_time,
                'error': 'Request timed out',
                'performance': 'TIMEOUT'
            }
            logger.error(f"⏰ {tenant_id} ({days}d): TIMEOUT after {execution_time:.2f}s")
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            result = {
                'endpoint': 'dashboard_overview',
                'tenant_id': tenant_id,
                'days': days,
                'status': 'EXCEPTION',
                'execution_time': execution_time,
                'error': str(e),
                'performance': 'FAILED'
            }
            logger.error(f"💥 {tenant_id} ({days}d): Exception - {str(e)}")
        
        self.test_results.append(result)
        return result
    
    async def test_geographic_data(self, tenant_id: str, days: int = 7) -> Dict:
        """Test geographic data endpoint"""
        url = f"{self.base_url}/dashboard/geographic/{tenant_id}"
        params = {'days': days}
        
        logger.info(f"🌍 Testing geographic data: {tenant_id} ({days} days)")
        
        start_time = time.time()
        
        try:
            async with self.session.get(url, params=params) as response:
                end_time = time.time()
                execution_time = end_time - start_time
                
                if response.status == 200:
                    data = await response.json()
                    
                    result = {
                        'endpoint': 'geographic_data',
                        'tenant_id': tenant_id,
                        'days': days,
                        'status': 'SUCCESS',
                        'status_code': response.status,
                        'execution_time': execution_time,
                        'response_size': len(json.dumps(data)),
                        'data_summary': {
                            'geographic_data_count': len(data.get('geographic_data', [])),
                            'total_locations': data.get('summary', {}).get('total_locations', 0)
                        }
                    }
                    
                    if execution_time < 1.0:
                        result['performance'] = 'EXCELLENT'
                        logger.info(f"✅ Geographic {tenant_id}: {execution_time:.2f}s - EXCELLENT")
                    elif execution_time < 3.0:
                        result['performance'] = 'GOOD'
                        logger.info(f"✅ Geographic {tenant_id}: {execution_time:.2f}s - GOOD")
                    else:
                        result['performance'] = 'SLOW'
                        logger.info(f"⚠️  Geographic {tenant_id}: {execution_time:.2f}s - SLOW")
                        
                else:
                    error_text = await response.text()
                    result = {
                        'endpoint': 'geographic_data',
                        'tenant_id': tenant_id,
                        'days': days,
                        'status': 'ERROR',
                        'status_code': response.status,
                        'execution_time': execution_time,
                        'error': error_text,
                        'performance': 'FAILED'
                    }
                    logger.error(f"❌ Geographic {tenant_id}: HTTP {response.status}")
                    
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            result = {
                'endpoint': 'geographic_data',
                'tenant_id': tenant_id,
                'days': days,
                'status': 'EXCEPTION',
                'execution_time': execution_time,
                'error': str(e),
                'performance': 'FAILED'
            }
            logger.error(f"💥 Geographic {tenant_id}: Exception - {str(e)}")
        
        self.test_results.append(result)
        return result
    
    async def test_tenants_list(self) -> Dict:
        """Test tenants list endpoint"""
        url = f"{self.base_url}/dashboard/tenants"
        
        logger.info("👥 Testing tenants list endpoint")
        
        start_time = time.time()
        
        try:
            async with self.session.get(url) as response:
                end_time = time.time()
                execution_time = end_time - start_time
                
                if response.status == 200:
                    data = await response.json()
                    tenants = data.get('tenants', [])
                    
                    result = {
                        'endpoint': 'tenants_list',
                        'status': 'SUCCESS',
                        'status_code': response.status,
                        'execution_time': execution_time,
                        'tenants_count': len(tenants),
                        'tenants': [t.get('id', 'unknown') for t in tenants[:5]]  # First 5 tenant IDs
                    }
                    
                    logger.info(f"✅ Tenants list: {execution_time:.2f}s - Found {len(tenants)} tenants")
                    
                else:
                    error_text = await response.text()
                    result = {
                        'endpoint': 'tenants_list',
                        'status': 'ERROR',
                        'status_code': response.status,
                        'execution_time': execution_time,
                        'error': error_text
                    }
                    logger.error(f"❌ Tenants list: HTTP {response.status}")
                    
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            result = {
                'endpoint': 'tenants_list',
                'status': 'EXCEPTION',
                'execution_time': execution_time,
                'error': str(e)
            }
            logger.error(f"💥 Tenants list: Exception - {str(e)}")
        
        self.test_results.append(result)
        return result
    
    def _validate_dashboard_response(self, data: Dict) -> Dict:
        """Validate dashboard response structure"""
        validation = {
            'valid': True,
            'missing_fields': [],
            'issues': []
        }
        
        # Required top-level fields
        required_fields = ['tenant_id', 'period', 'summary', 'daily_stats', 'hourly_trends', 
                          'top_endpoints', 'top_users', 'status_distribution', 'geographic_data']
        
        for field in required_fields:
            if field not in data:
                validation['missing_fields'].append(field)
                validation['valid'] = False
        
        # Validate summary structure
        if 'summary' in data:
            summary_fields = ['total_requests', 'unique_users', 'avg_response_time', 'total_errors', 'error_rate']
            for field in summary_fields:
                if field not in data['summary']:
                    validation['issues'].append(f'Missing summary.{field}')
                    validation['valid'] = False
        
        # Validate arrays
        array_fields = ['daily_stats', 'hourly_trends', 'top_endpoints', 'top_users', 'geographic_data']
        for field in array_fields:
            if field in data and not isinstance(data[field], list):
                validation['issues'].append(f'{field} should be an array')
                validation['valid'] = False
        
        return validation
    
    async def test_cache_effectiveness(self, tenant_id: str, days: int = 7) -> Dict:
        """Test cache effectiveness by making repeated requests"""
        logger.info(f"🔄 Testing cache effectiveness for {tenant_id}")
        
        # Make 3 consecutive requests
        times = []
        for i in range(3):
            logger.info(f"   Request {i+1}/3...")
            result = await self.test_dashboard_overview(tenant_id, days)
            if result['status'] == 'SUCCESS':
                times.append(result['execution_time'])
            await asyncio.sleep(0.5)  # Small delay between requests
        
        if len(times) >= 2:
            first_time = times[0]
            subsequent_times = times[1:]
            avg_subsequent = statistics.mean(subsequent_times)
            
            speedup = first_time / avg_subsequent if avg_subsequent > 0 else 1
            cache_working = speedup > 1.5  # Cache should provide at least 1.5x speedup
            
            cache_result = {
                'cache_test': True,
                'first_request_time': first_time,
                'subsequent_avg_time': avg_subsequent,
                'speedup': speedup,
                'cache_working': cache_working,
                'all_times': times
            }
            
            if cache_working:
                logger.info(f"✅ Cache working: {speedup:.2f}x speedup")
            else:
                logger.warning(f"⚠️  Cache may not be working: {speedup:.2f}x speedup")
                
            return cache_result
        else:
            return {'cache_test': False, 'error': 'Not enough successful requests'}
    
    async def run_load_test(self, tenant_id: str, concurrent_requests: int = 5, days: int = 7) -> Dict:
        """Run concurrent load test"""
        logger.info(f"🚀 Running load test: {concurrent_requests} concurrent requests for {tenant_id}")
        
        start_time = time.time()
        
        # Create concurrent tasks
        tasks = []
        for i in range(concurrent_requests):
            task = self.test_dashboard_overview(tenant_id, days)
            tasks.append(task)
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Analyze results
        successful = 0
        failed = 0
        timeouts = 0
        execution_times = []
        
        for result in results:
            if isinstance(result, Exception):
                failed += 1
            elif result['status'] == 'SUCCESS':
                successful += 1
                execution_times.append(result['execution_time'])
            elif result['status'] == 'TIMEOUT':
                timeouts += 1
            else:
                failed += 1
        
        load_test_result = {
            'concurrent_requests': concurrent_requests,
            'total_time': total_time,
            'successful': successful,
            'failed': failed,
            'timeouts': timeouts,
            'success_rate': (successful / concurrent_requests) * 100,
            'avg_response_time': statistics.mean(execution_times) if execution_times else 0,
            'min_response_time': min(execution_times) if execution_times else 0,
            'max_response_time': max(execution_times) if execution_times else 0
        }
        
        logger.info(f"📊 Load test results:")
        logger.info(f"   Success rate: {load_test_result['success_rate']:.1f}%")
        logger.info(f"   Avg response time: {load_test_result['avg_response_time']:.2f}s")
        logger.info(f"   Total time: {total_time:.2f}s")
        
        return load_test_result

    async def run_comprehensive_test_suite(self, tenant_ids: List[str] = None) -> Dict:
        """Run comprehensive test suite"""
        if not tenant_ids:
            tenant_ids = ['b2c']  # Default test tenant

        logger.info("🧪 Starting comprehensive dashboard endpoint test suite")
        logger.info("=" * 60)

        test_suite_results = {
            'start_time': datetime.now(timezone.utc).isoformat(),
            'base_url': self.base_url,
            'tenant_ids': tenant_ids,
            'tests': {}
        }

        # Test 1: Basic endpoint availability
        logger.info("📋 Test 1: Basic endpoint availability")
        tenants_result = await self.test_tenants_list()
        test_suite_results['tests']['tenants_list'] = tenants_result

        # Test 2: Dashboard overview for different time ranges
        logger.info("📊 Test 2: Dashboard overview - different time ranges")
        time_ranges = [1, 7, 30, 90]
        overview_results = {}

        for tenant_id in tenant_ids:
            tenant_results = {}
            for days in time_ranges:
                result = await self.test_dashboard_overview(tenant_id, days)
                tenant_results[f'{days}_days'] = result
                await asyncio.sleep(0.5)  # Small delay between requests
            overview_results[tenant_id] = tenant_results

        test_suite_results['tests']['dashboard_overview'] = overview_results

        # Test 3: Geographic data
        logger.info("🌍 Test 3: Geographic data endpoints")
        geographic_results = {}
        for tenant_id in tenant_ids:
            result = await self.test_geographic_data(tenant_id, 7)
            geographic_results[tenant_id] = result

        test_suite_results['tests']['geographic_data'] = geographic_results

        # Test 4: Cache effectiveness
        logger.info("🔄 Test 4: Cache effectiveness")
        cache_results = {}
        for tenant_id in tenant_ids:
            result = await self.test_cache_effectiveness(tenant_id, 7)
            cache_results[tenant_id] = result

        test_suite_results['tests']['cache_effectiveness'] = cache_results

        # Test 5: Load testing
        logger.info("🚀 Test 5: Concurrent load testing")
        load_test_scenarios = [3, 5, 10]  # Different concurrency levels
        load_results = {}

        for tenant_id in tenant_ids:
            tenant_load_results = {}
            for concurrent in load_test_scenarios:
                logger.info(f"   Testing {concurrent} concurrent requests for {tenant_id}")
                result = await self.run_load_test(tenant_id, concurrent, 7)
                tenant_load_results[f'{concurrent}_concurrent'] = result
                await asyncio.sleep(2)  # Pause between load tests

            load_results[tenant_id] = tenant_load_results

        test_suite_results['tests']['load_testing'] = load_results

        # Generate summary
        test_suite_results['summary'] = self._generate_test_summary()
        test_suite_results['end_time'] = datetime.now(timezone.utc).isoformat()

        return test_suite_results

    def _generate_test_summary(self) -> Dict:
        """Generate test summary from all results"""
        summary = {
            'total_tests': len(self.test_results),
            'successful': 0,
            'failed': 0,
            'timeouts': 0,
            'performance_breakdown': {
                'excellent': 0,
                'good': 0,
                'acceptable': 0,
                'poor': 0
            },
            'avg_response_time': 0,
            'recommendations': []
        }

        execution_times = []

        for result in self.test_results:
            if result['status'] == 'SUCCESS':
                summary['successful'] += 1
                execution_times.append(result['execution_time'])

                # Performance categorization
                perf = result.get('performance', 'UNKNOWN')
                if perf == 'EXCELLENT':
                    summary['performance_breakdown']['excellent'] += 1
                elif perf == 'GOOD':
                    summary['performance_breakdown']['good'] += 1
                elif perf == 'ACCEPTABLE':
                    summary['performance_breakdown']['acceptable'] += 1
                elif perf == 'POOR':
                    summary['performance_breakdown']['poor'] += 1

            elif result['status'] == 'TIMEOUT':
                summary['timeouts'] += 1
            else:
                summary['failed'] += 1

        if execution_times:
            summary['avg_response_time'] = statistics.mean(execution_times)
            summary['min_response_time'] = min(execution_times)
            summary['max_response_time'] = max(execution_times)

        # Generate recommendations
        if summary['failed'] > 0:
            summary['recommendations'].append("Some endpoints are failing - check server logs and database connectivity")

        if summary['timeouts'] > 0:
            summary['recommendations'].append("Timeout issues detected - consider increasing timeout values or optimizing queries")

        if summary['performance_breakdown']['poor'] > 0:
            summary['recommendations'].append("Poor performance detected - run database optimization scripts")

        if summary['avg_response_time'] > 5:
            summary['recommendations'].append("Average response time is high - check database indexes and query optimization")

        return summary

    def print_test_report(self, results: Dict):
        """Print formatted test report"""
        logger.info("📋 DASHBOARD ENDPOINT TEST REPORT")
        logger.info("=" * 60)

        summary = results.get('summary', {})

        # Overall statistics
        logger.info(f"🔢 Overall Statistics:")
        logger.info(f"   Total tests: {summary.get('total_tests', 0)}")
        logger.info(f"   Successful: {summary.get('successful', 0)}")
        logger.info(f"   Failed: {summary.get('failed', 0)}")
        logger.info(f"   Timeouts: {summary.get('timeouts', 0)}")

        if summary.get('avg_response_time'):
            logger.info(f"   Avg response time: {summary['avg_response_time']:.2f}s")
            logger.info(f"   Min response time: {summary.get('min_response_time', 0):.2f}s")
            logger.info(f"   Max response time: {summary.get('max_response_time', 0):.2f}s")

        # Performance breakdown
        perf = summary.get('performance_breakdown', {})
        logger.info(f"⚡ Performance Breakdown:")
        logger.info(f"   Excellent (< 2s): {perf.get('excellent', 0)}")
        logger.info(f"   Good (2-5s): {perf.get('good', 0)}")
        logger.info(f"   Acceptable (5-10s): {perf.get('acceptable', 0)}")
        logger.info(f"   Poor (> 10s): {perf.get('poor', 0)}")

        # Cache effectiveness
        cache_tests = results.get('tests', {}).get('cache_effectiveness', {})
        for tenant_id, cache_result in cache_tests.items():
            if cache_result.get('cache_working'):
                speedup = cache_result.get('speedup', 1)
                logger.info(f"🔄 Cache effectiveness ({tenant_id}): {speedup:.2f}x speedup - ✅ Working")
            else:
                logger.info(f"🔄 Cache effectiveness ({tenant_id}): ❌ Not working optimally")

        # Load test results
        load_tests = results.get('tests', {}).get('load_testing', {})
        for tenant_id, load_results in load_tests.items():
            logger.info(f"🚀 Load test results ({tenant_id}):")
            for scenario, result in load_results.items():
                success_rate = result.get('success_rate', 0)
                avg_time = result.get('avg_response_time', 0)
                logger.info(f"   {scenario}: {success_rate:.1f}% success, {avg_time:.2f}s avg")

        # Recommendations
        recommendations = summary.get('recommendations', [])
        if recommendations:
            logger.info("💡 Recommendations:")
            for i, rec in enumerate(recommendations, 1):
                logger.info(f"   {i}. {rec}")
        else:
            logger.info("🎉 No issues detected - dashboard is performing well!")

        logger.info("=" * 60)

async def main():
    """Main test execution function"""
    import argparse

    parser = argparse.ArgumentParser(description='Test Dashboard API Endpoints')
    parser.add_argument('--url', default='http://localhost:8000', help='API base URL')
    parser.add_argument('--tenant', action='append', help='Tenant ID to test (can be used multiple times)')
    parser.add_argument('--auth-token', help='Authentication token')
    parser.add_argument('--quick', action='store_true', help='Run quick test only')
    parser.add_argument('--load-only', action='store_true', help='Run load test only')
    parser.add_argument('--cache-only', action='store_true', help='Run cache test only')

    args = parser.parse_args()

    # Default tenants if none specified
    tenant_ids = args.tenant or ['b2c']

    logger.info(f"🚀 Starting dashboard endpoint tests")
    logger.info(f"   API URL: {args.url}")
    logger.info(f"   Tenants: {', '.join(tenant_ids)}")

    async with DashboardEndpointTester(args.url, args.auth_token) as tester:
        try:
            if args.quick:
                # Quick test - just basic functionality
                logger.info("⚡ Running quick test...")
                for tenant_id in tenant_ids:
                    await tester.test_dashboard_overview(tenant_id, 7)
                    await tester.test_geographic_data(tenant_id, 7)

            elif args.load_only:
                # Load test only
                logger.info("🚀 Running load test only...")
                for tenant_id in tenant_ids:
                    await tester.run_load_test(tenant_id, 10, 7)

            elif args.cache_only:
                # Cache test only
                logger.info("🔄 Running cache test only...")
                for tenant_id in tenant_ids:
                    await tester.test_cache_effectiveness(tenant_id, 7)

            else:
                # Full comprehensive test suite
                results = await tester.run_comprehensive_test_suite(tenant_ids)

                # Print report
                tester.print_test_report(results)

                # Save detailed results
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_file = f"dashboard_test_results_{timestamp}.json"

                with open(output_file, 'w') as f:
                    json.dump(results, f, indent=2, default=str)

                logger.info(f"📄 Detailed results saved to: {output_file}")

        except KeyboardInterrupt:
            logger.info("⏹️  Test interrupted by user")
        except Exception as e:
            logger.error(f"❌ Test suite failed: {str(e)}")
            raise

if __name__ == "__main__":
    asyncio.run(main())
