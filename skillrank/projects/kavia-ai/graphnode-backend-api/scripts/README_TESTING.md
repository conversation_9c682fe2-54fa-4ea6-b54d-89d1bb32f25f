# Dashboard API Testing Suite

This directory contains comprehensive testing scripts for the Dashboard API endpoints to validate performance optimizations and identify bottlenecks.

## 🧪 Available Test Scripts

### 1. **Quick Dashboard Test** (`quick_dashboard_test.py`)
**Purpose**: Fast validation of basic dashboard functionality
**Best for**: Quick health checks and development testing

```bash
# Basic test
python3 quick_dashboard_test.py

# Test against different API
python3 quick_dashboard_test.py --url http://your-api.com --tenant your-tenant
```

**Features**:
- ✅ Tests all main endpoints (overview, geographic, tenants)
- ⏱️ Measures response times
- 🔄 Tests cache effectiveness
- 📊 Provides performance assessment
- 🚀 Runs in under 2 minutes

### 2. **Comprehensive Endpoint Test** (`test_dashboard_endpoint.py`)
**Purpose**: Detailed testing with multiple scenarios and validation
**Best for**: Thorough testing before production deployment

```bash
# Full test suite
python3 test_dashboard_endpoint.py --url http://localhost:8000 --tenant b2c

# Quick test only
python3 test_dashboard_endpoint.py --quick

# Cache test only
python3 test_dashboard_endpoint.py --cache-only

# Load test only
python3 test_dashboard_endpoint.py --load-only
```

**Features**:
- 🔍 Response structure validation
- 📈 Multiple time range testing (1, 7, 30, 90 days)
- 🔄 Cache effectiveness analysis
- 🚀 Concurrent load testing
- 📊 Detailed performance metrics
- 💡 Automated recommendations

### 3. **Load Testing** (`dashboard_load_test.py`)
**Purpose**: Stress testing with concurrent requests
**Best for**: Performance validation under load

```bash
# Full load test suite
python3 dashboard_load_test.py --url http://localhost:8000 --tenant b2c

# Custom load test
python3 dashboard_load_test.py --concurrent 10 --requests 100 --tenant b2c
```

**Features**:
- 🚀 Multiple load scenarios (light, medium, heavy)
- 📊 Detailed performance statistics (avg, median, p95, p99)
- 🔍 Error analysis and breakdown
- ⚡ Concurrent request handling
- 📈 Requests per second measurement

### 4. **Performance Monitoring** (`monitor_dashboard_performance.py`)
**Purpose**: Database and application performance analysis
**Best for**: Identifying optimization opportunities

```bash
# Monitor performance for default tenant
python3 monitor_dashboard_performance.py

# Monitor specific tenant
python3 monitor_dashboard_performance.py your-tenant-id
```

**Features**:
- 🗃️ Database index verification
- 📊 Collection statistics
- ⏱️ Query performance measurement
- 🔄 Cache effectiveness testing
- 💡 Optimization recommendations

### 5. **Database Index Creation** (`create_dashboard_indexes.py`)
**Purpose**: Create optimized database indexes
**Best for**: Initial setup and performance optimization

```bash
python3 create_dashboard_indexes.py
```

**Features**:
- 🗃️ Creates 8 optimized indexes
- ⏱️ TTL index for automatic cleanup
- 📊 Performance analysis
- 🔍 Index verification

## 🚀 Quick Start - All-in-One Test Runner

Use the comprehensive test runner for complete validation:

```bash
# Run all tests (recommended)
./run_dashboard_tests.sh

# Test against different API
./run_dashboard_tests.sh --url http://your-api.com --tenant your-tenant

# Quick tests only
./run_dashboard_tests.sh --quick-only

# Skip database optimization
./run_dashboard_tests.sh --skip-optimization
```

## 📊 Understanding Test Results

### Performance Categories
- 🟢 **EXCELLENT**: < 2 seconds response time
- 🟡 **GOOD**: 2-5 seconds response time  
- 🟠 **ACCEPTABLE**: 5-10 seconds response time
- 🔴 **POOR**: > 10 seconds response time

### Success Rate Thresholds
- **99%+**: Excellent reliability
- **95-99%**: Good reliability
- **90-95%**: Acceptable reliability
- **<90%**: Poor reliability - investigation needed

### Cache Effectiveness
- **2x+ speedup**: Cache working well
- **1.5-2x speedup**: Cache working acceptably
- **<1.5x speedup**: Cache issues - needs investigation

## 🔧 Troubleshooting Common Issues

### 1. **Connection Errors**
```
❌ ERROR: Connection refused
```
**Solution**: Ensure your API server is running on the specified URL

### 2. **Timeout Issues**
```
⏰ TIMEOUT after 60.00s
```
**Solutions**:
- Run database optimization: `python3 create_dashboard_indexes.py`
- Check database connection and performance
- Verify MongoDB indexes exist

### 3. **High Response Times**
```
🔴 Performance: POOR (> 10s)
```
**Solutions**:
- Create database indexes
- Check collection size and implement data retention
- Verify connection pool settings
- Monitor database server resources

### 4. **Cache Not Working**
```
🔄 Cache: ⚠️ Not optimal
```
**Solutions**:
- Check Redis/cache server connectivity
- Verify cache TTL settings
- Review cache key generation logic

## 📈 Expected Performance After Optimization

| Metric | Before Optimization | After Optimization | Improvement |
|--------|-------------------|-------------------|-------------|
| Response Time | 30-60+ seconds | 2-5 seconds | **85-90% faster** |
| Cache Hit Rate | 0% | 80%+ | **Instant responses** |
| Concurrent Users | Limited | 10x more | **Better scalability** |
| Success Rate | Variable | 99%+ | **Reliable service** |

## 🔍 Analyzing Results

### JSON Report Structure
Each test generates detailed JSON reports with:

```json
{
  "summary": {
    "total_tests": 15,
    "successful": 14,
    "avg_response_time": 2.34,
    "performance_breakdown": {...}
  },
  "tests": {
    "dashboard_overview": {...},
    "geographic_data": {...},
    "cache_effectiveness": {...}
  },
  "recommendations": [...]
}
```

### Key Metrics to Monitor
1. **Average Response Time**: Should be < 5 seconds
2. **Success Rate**: Should be > 95%
3. **Cache Speedup**: Should be > 2x
4. **P95 Response Time**: Should be < 10 seconds
5. **Error Rate**: Should be < 1%

## 🛠️ Integration with CI/CD

Add to your CI/CD pipeline:

```yaml
# Example GitHub Actions step
- name: Test Dashboard Performance
  run: |
    cd graphnode-backend-api/scripts
    ./run_dashboard_tests.sh --quick-only --url ${{ env.API_URL }}
```

## 📞 Support

If tests consistently fail or show poor performance:

1. **Check Prerequisites**:
   - MongoDB is running and accessible
   - Required indexes are created
   - API server is running
   - Network connectivity is stable

2. **Review Logs**:
   - API server logs for errors
   - MongoDB logs for slow queries
   - System resource usage

3. **Run Diagnostics**:
   ```bash
   python3 monitor_dashboard_performance.py
   ```

4. **Apply Optimizations**:
   ```bash
   python3 create_dashboard_indexes.py
   ```

## 📝 Test Report Examples

### Successful Test Output
```
🎉 Overall: EXCELLENT - Dashboard is performing very well!
✅ Success Rate: 100.0% (15/15)
⏱️ Average Response Time: 2.34s
🔄 Cache: ✅ Working (3.2x speedup)
```

### Issues Detected Output
```
⚠️ Overall: ACCEPTABLE - Some issues detected
✅ Success Rate: 87.5% (14/16)
⏱️ Average Response Time: 8.45s
🔄 Cache: ⚠️ Not optimal

💡 Recommendations:
1. Run database optimization scripts
2. Check MongoDB indexes
3. Review cache configuration
```
