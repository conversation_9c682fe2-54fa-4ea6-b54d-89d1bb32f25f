#!/usr/bin/env python3
"""
Database Index Creation Script for Dashboard Optimization
This script creates optimized indexes for the API tracking dashboard
"""

import asyncio
import logging
from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.errors import OperationFailure
import sys
import os

# Add the parent directory to the path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.Settings import settings
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DashboardIndexManager:
    def __init__(self):
        self.client = None
        self.db = None
        self.collection_name = "request_tracker"
        
    def connect(self):
        """Connect to MongoDB"""
        try:
            self.client = MongoClient(
                settings.MONGO_CONNECTION_URI,
                serverSelectionTimeoutMS=5000,
                connectTimeoutMS=5000
            )
            self.db = self.client[KAVIA_ROOT_DB_NAME]
            # Test connection
            self.client.admin.command('ping')
            logger.info(f"✅ Connected to MongoDB: {KAVIA_ROOT_DB_NAME}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to connect to MongoDB: {str(e)}")
            return False
    
    def get_collection_stats(self):
        """Get collection statistics"""
        try:
            collection = self.db[self.collection_name]
            stats = self.db.command("collStats", self.collection_name)
            count = collection.count_documents({})
            
            logger.info(f"📊 Collection Stats for {self.collection_name}:")
            logger.info(f"   Documents: {count:,}")
            logger.info(f"   Size: {stats.get('size', 0) / 1024 / 1024:.2f} MB")
            logger.info(f"   Storage Size: {stats.get('storageSize', 0) / 1024 / 1024:.2f} MB")
            logger.info(f"   Indexes: {stats.get('nindexes', 0)}")
            
            return stats
        except Exception as e:
            logger.error(f"❌ Failed to get collection stats: {str(e)}")
            return None
    
    def list_existing_indexes(self):
        """List existing indexes"""
        try:
            collection = self.db[self.collection_name]
            indexes = list(collection.list_indexes())
            
            logger.info(f"📋 Existing indexes for {self.collection_name}:")
            for idx in indexes:
                logger.info(f"   {idx['name']}: {idx.get('key', {})}")
            
            return indexes
        except Exception as e:
            logger.error(f"❌ Failed to list indexes: {str(e)}")
            return []
    
    def create_dashboard_indexes(self):
        """Create optimized indexes for dashboard queries"""
        collection = self.db[self.collection_name]
        indexes_created = 0
        
        # Define indexes for dashboard optimization
        dashboard_indexes = [
            {
                'name': 'tenant_created_at_idx',
                'keys': [('tenant_id', ASCENDING), ('created_at', DESCENDING)],
                'background': True,
                'comment': 'Primary index for dashboard queries by tenant and time range'
            },
            {
                'name': 'tenant_date_partition_idx', 
                'keys': [('tenant_id', ASCENDING), ('date_partition', ASCENDING)],
                'background': True,
                'comment': 'Index for daily stats aggregation'
            },
            {
                'name': 'tenant_hour_partition_idx',
                'keys': [('tenant_id', ASCENDING), ('hour_partition', ASCENDING)],
                'background': True,
                'comment': 'Index for hourly trends aggregation'
            },
            {
                'name': 'tenant_status_code_idx',
                'keys': [('tenant_id', ASCENDING), ('status_code', ASCENDING), ('created_at', DESCENDING)],
                'background': True,
                'comment': 'Index for status distribution and error analysis'
            },
            {
                'name': 'tenant_endpoint_idx',
                'keys': [('tenant_id', ASCENDING), ('method', ASCENDING), ('endpoint', ASCENDING), ('created_at', DESCENDING)],
                'background': True,
                'comment': 'Index for top endpoints analysis'
            },
            {
                'name': 'tenant_user_idx',
                'keys': [('tenant_id', ASCENDING), ('user_id', ASCENDING), ('created_at', DESCENDING)],
                'background': True,
                'comment': 'Index for top users analysis'
            },
            {
                'name': 'tenant_geo_idx',
                'keys': [('tenant_id', ASCENDING), ('country', ASCENDING), ('region', ASCENDING), ('created_at', DESCENDING)],
                'background': True,
                'comment': 'Index for geographic data analysis'
            },
            {
                'name': 'created_at_ttl_idx',
                'keys': [('created_at', ASCENDING)],
                'background': True,
                'expireAfterSeconds': 7776000,  # 90 days TTL
                'comment': 'TTL index for automatic data cleanup after 90 days'
            }
        ]
        
        for index_def in dashboard_indexes:
            try:
                index_name = index_def['name']
                keys = index_def['keys']
                options = {k: v for k, v in index_def.items() if k not in ['name', 'keys']}
                
                # Check if index already exists
                existing_indexes = collection.list_indexes()
                index_exists = any(idx['name'] == index_name for idx in existing_indexes)
                
                if index_exists:
                    logger.info(f"⏭️  Index {index_name} already exists, skipping")
                    continue
                
                logger.info(f"🔨 Creating index: {index_name}")
                logger.info(f"   Keys: {keys}")
                logger.info(f"   Options: {options}")
                
                collection.create_index(keys, name=index_name, **options)
                indexes_created += 1
                logger.info(f"✅ Created index: {index_name}")
                
            except OperationFailure as e:
                if "already exists" in str(e):
                    logger.info(f"⏭️  Index {index_name} already exists")
                else:
                    logger.error(f"❌ Failed to create index {index_name}: {str(e)}")
            except Exception as e:
                logger.error(f"❌ Unexpected error creating index {index_name}: {str(e)}")
        
        return indexes_created
    
    def analyze_query_performance(self):
        """Analyze query performance for common dashboard queries"""
        collection = self.db[self.collection_name]
        
        # Sample queries that the dashboard runs
        test_queries = [
            {
                'name': 'Summary metrics query',
                'query': {'tenant_id': 'b2c', 'created_at': {'$gte': '2024-01-01', '$lte': '2024-12-31'}}
            },
            {
                'name': 'Daily stats query', 
                'query': {'tenant_id': 'b2c', 'date_partition': {'$in': ['2024-01-01', '2024-01-02']}}
            },
            {
                'name': 'Hourly trends query',
                'query': {'tenant_id': 'b2c', 'hour_partition': {'$in': [0, 1, 2, 3, 4, 5]}}
            }
        ]
        
        logger.info("🔍 Analyzing query performance:")
        
        for test in test_queries:
            try:
                explain_result = collection.find(test['query']).explain()
                execution_stats = explain_result.get('executionStats', {})
                
                logger.info(f"   {test['name']}:")
                logger.info(f"     Execution time: {execution_stats.get('executionTimeMillis', 'N/A')} ms")
                logger.info(f"     Documents examined: {execution_stats.get('totalDocsExamined', 'N/A')}")
                logger.info(f"     Documents returned: {execution_stats.get('totalDocsReturned', 'N/A')}")
                logger.info(f"     Index used: {execution_stats.get('indexName', 'No index')}")
                
            except Exception as e:
                logger.error(f"❌ Failed to analyze query {test['name']}: {str(e)}")
    
    def cleanup_old_data(self, days_to_keep=90):
        """Clean up old tracking data"""
        from datetime import datetime, timezone, timedelta
        
        collection = self.db[self.collection_name]
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
        
        try:
            result = collection.delete_many({'created_at': {'$lt': cutoff_date}})
            logger.info(f"🧹 Cleaned up {result.deleted_count} old documents (older than {days_to_keep} days)")
            return result.deleted_count
        except Exception as e:
            logger.error(f"❌ Failed to cleanup old data: {str(e)}")
            return 0
    
    def close(self):
        """Close database connection"""
        if self.client:
            self.client.close()
            logger.info("🔌 Disconnected from MongoDB")

def main():
    """Main function to run index creation and optimization"""
    manager = DashboardIndexManager()
    
    if not manager.connect():
        sys.exit(1)
    
    try:
        # Get current stats
        manager.get_collection_stats()
        
        # List existing indexes
        manager.list_existing_indexes()
        
        # Create dashboard indexes
        logger.info("🚀 Creating dashboard indexes...")
        indexes_created = manager.create_dashboard_indexes()
        logger.info(f"✅ Created {indexes_created} new indexes")
        
        # Analyze query performance
        manager.analyze_query_performance()
        
        # Optional: cleanup old data (uncomment if needed)
        # logger.info("🧹 Cleaning up old data...")
        # deleted_count = manager.cleanup_old_data(90)
        
        logger.info("🎉 Dashboard optimization completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Optimization failed: {str(e)}")
        sys.exit(1)
    finally:
        manager.close()

if __name__ == "__main__":
    main()
