#!/usr/bin/env python3
"""
Dashboard Performance Monitoring Script
This script monitors and reports on dashboard query performance
"""

import asyncio
import time
import logging
from datetime import datetime, timezone, timedelta
import sys
import os
import json

# Add the parent directory to the path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.enhanced_tracking_service import enhanced_tracking_service
from app.connection.mongo_client import get_db_client
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DashboardPerformanceMonitor:
    def __init__(self):
        self.collection_name = "request_tracker"
        self.test_tenant_id = "b2c"  # Default test tenant
        
    async def test_dashboard_performance(self, tenant_id: str = None, days: int = 7):
        """Test dashboard performance with timing"""
        if not tenant_id:
            tenant_id = self.test_tenant_id
            
        logger.info(f"🚀 Testing dashboard performance for tenant: {tenant_id}, days: {days}")
        
        start_time = time.time()
        
        try:
            # Test the main dashboard data method
            dashboard_data = await enhanced_tracking_service.get_dashboard_data(tenant_id, days)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Log performance metrics
            logger.info(f"✅ Dashboard query completed in {total_time:.2f} seconds")
            logger.info(f"📊 Results summary:")
            logger.info(f"   Total requests: {dashboard_data.get('summary', {}).get('total_requests', 0)}")
            logger.info(f"   Unique users: {dashboard_data.get('summary', {}).get('unique_users', 0)}")
            logger.info(f"   Daily stats entries: {len(dashboard_data.get('daily_stats', []))}")
            logger.info(f"   Top endpoints: {len(dashboard_data.get('top_endpoints', []))}")
            logger.info(f"   Geographic data points: {len(dashboard_data.get('geographic_data', []))}")
            
            # Performance assessment
            if total_time < 2.0:
                logger.info("🟢 Performance: EXCELLENT (< 2s)")
            elif total_time < 5.0:
                logger.info("🟡 Performance: GOOD (2-5s)")
            elif total_time < 10.0:
                logger.info("🟠 Performance: ACCEPTABLE (5-10s)")
            else:
                logger.info("🔴 Performance: POOR (> 10s)")
                
            return {
                'success': True,
                'execution_time': total_time,
                'data_summary': {
                    'total_requests': dashboard_data.get('summary', {}).get('total_requests', 0),
                    'unique_users': dashboard_data.get('summary', {}).get('unique_users', 0),
                    'daily_stats_count': len(dashboard_data.get('daily_stats', [])),
                    'top_endpoints_count': len(dashboard_data.get('top_endpoints', [])),
                    'geographic_data_count': len(dashboard_data.get('geographic_data', []))
                }
            }
            
        except Exception as e:
            end_time = time.time()
            total_time = end_time - start_time
            
            logger.error(f"❌ Dashboard query failed after {total_time:.2f} seconds: {str(e)}")
            return {
                'success': False,
                'execution_time': total_time,
                'error': str(e)
            }
    
    async def test_cache_performance(self, tenant_id: str = None, days: int = 7):
        """Test cache performance by running the same query twice"""
        if not tenant_id:
            tenant_id = self.test_tenant_id
            
        logger.info(f"🔄 Testing cache performance for tenant: {tenant_id}")
        
        # First query (should hit database)
        logger.info("📊 First query (database hit)...")
        result1 = await self.test_dashboard_performance(tenant_id, days)
        
        # Wait a moment
        await asyncio.sleep(1)
        
        # Second query (should hit cache)
        logger.info("📋 Second query (cache hit)...")
        result2 = await self.test_dashboard_performance(tenant_id, days)
        
        if result1['success'] and result2['success']:
            speedup = result1['execution_time'] / result2['execution_time']
            logger.info(f"🚀 Cache speedup: {speedup:.2f}x faster")
            logger.info(f"   First query: {result1['execution_time']:.2f}s")
            logger.info(f"   Second query: {result2['execution_time']:.2f}s")
            
            return {
                'cache_working': result2['execution_time'] < result1['execution_time'],
                'speedup': speedup,
                'first_query_time': result1['execution_time'],
                'second_query_time': result2['execution_time']
            }
        else:
            return {'cache_working': False, 'error': 'One or both queries failed'}
    
    def check_database_indexes(self):
        """Check if required indexes exist"""
        logger.info("🔍 Checking database indexes...")
        
        try:
            client = get_db_client()
            db = client[KAVIA_ROOT_DB_NAME]
            collection = db[self.collection_name]
            
            indexes = list(collection.list_indexes())
            index_names = [idx['name'] for idx in indexes]
            
            # Required indexes for optimal performance
            required_indexes = [
                'tenant_created_at_idx',
                'tenant_date_partition_idx',
                'tenant_hour_partition_idx',
                'tenant_status_code_idx',
                'tenant_endpoint_idx',
                'tenant_user_idx',
                'tenant_geo_idx'
            ]
            
            missing_indexes = []
            existing_indexes = []
            
            for req_idx in required_indexes:
                if req_idx in index_names:
                    existing_indexes.append(req_idx)
                    logger.info(f"✅ Index exists: {req_idx}")
                else:
                    missing_indexes.append(req_idx)
                    logger.warning(f"❌ Missing index: {req_idx}")
            
            if missing_indexes:
                logger.warning(f"⚠️  Missing {len(missing_indexes)} required indexes")
                logger.warning("   Run: python scripts/create_dashboard_indexes.py")
            else:
                logger.info("🎉 All required indexes are present")
                
            return {
                'total_indexes': len(indexes),
                'existing_required': existing_indexes,
                'missing_required': missing_indexes,
                'all_indexes_present': len(missing_indexes) == 0
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to check indexes: {str(e)}")
            return {'error': str(e)}
    
    def get_collection_stats(self):
        """Get collection statistics"""
        logger.info("📊 Getting collection statistics...")
        
        try:
            client = get_db_client()
            db = client[KAVIA_ROOT_DB_NAME]
            
            stats = db.command("collStats", self.collection_name)
            count = db[self.collection_name].count_documents({})
            
            size_mb = stats.get('size', 0) / 1024 / 1024
            storage_size_mb = stats.get('storageSize', 0) / 1024 / 1024
            
            logger.info(f"📈 Collection: {self.collection_name}")
            logger.info(f"   Documents: {count:,}")
            logger.info(f"   Data size: {size_mb:.2f} MB")
            logger.info(f"   Storage size: {storage_size_mb:.2f} MB")
            logger.info(f"   Indexes: {stats.get('nindexes', 0)}")
            logger.info(f"   Average document size: {stats.get('avgObjSize', 0)} bytes")
            
            return {
                'document_count': count,
                'data_size_mb': size_mb,
                'storage_size_mb': storage_size_mb,
                'index_count': stats.get('nindexes', 0),
                'avg_document_size': stats.get('avgObjSize', 0)
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get collection stats: {str(e)}")
            return {'error': str(e)}
    
    async def run_comprehensive_test(self, tenant_id: str = None):
        """Run comprehensive performance test"""
        logger.info("🧪 Running comprehensive dashboard performance test...")
        
        results = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'tenant_id': tenant_id or self.test_tenant_id
        }
        
        # Check database setup
        results['collection_stats'] = self.get_collection_stats()
        results['index_check'] = self.check_database_indexes()
        
        # Test different time ranges
        time_ranges = [1, 7, 30]
        results['performance_tests'] = {}
        
        for days in time_ranges:
            logger.info(f"🔄 Testing {days} day(s) range...")
            test_result = await self.test_dashboard_performance(tenant_id, days)
            results['performance_tests'][f'{days}_days'] = test_result
        
        # Test cache performance
        logger.info("🔄 Testing cache performance...")
        results['cache_test'] = await self.test_cache_performance(tenant_id, 7)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(results)
        results['recommendations'] = recommendations
        
        return results
    
    def _generate_recommendations(self, results):
        """Generate performance recommendations based on test results"""
        recommendations = []
        
        # Check if indexes are missing
        index_check = results.get('index_check', {})
        if not index_check.get('all_indexes_present', False):
            recommendations.append({
                'priority': 'HIGH',
                'issue': 'Missing database indexes',
                'solution': 'Run: python scripts/create_dashboard_indexes.py',
                'impact': 'Significant performance improvement expected'
            })
        
        # Check performance times
        perf_tests = results.get('performance_tests', {})
        slow_queries = []
        
        for time_range, test_result in perf_tests.items():
            if test_result.get('success') and test_result.get('execution_time', 0) > 5:
                slow_queries.append(time_range)
        
        if slow_queries:
            recommendations.append({
                'priority': 'MEDIUM',
                'issue': f'Slow queries detected for: {", ".join(slow_queries)}',
                'solution': 'Consider data archiving or query optimization',
                'impact': 'Improved user experience'
            })
        
        # Check collection size
        stats = results.get('collection_stats', {})
        if stats.get('document_count', 0) > 1000000:  # 1M documents
            recommendations.append({
                'priority': 'MEDIUM',
                'issue': 'Large collection size',
                'solution': 'Implement data retention policy (TTL indexes)',
                'impact': 'Reduced storage and improved query performance'
            })
        
        # Check cache performance
        cache_test = results.get('cache_test', {})
        if not cache_test.get('cache_working', False):
            recommendations.append({
                'priority': 'LOW',
                'issue': 'Cache not providing expected speedup',
                'solution': 'Review cache implementation and TTL settings',
                'impact': 'Better response times for repeated queries'
            })
        
        return recommendations

async def main():
    """Main function"""
    monitor = DashboardPerformanceMonitor()
    
    # Allow tenant_id to be passed as command line argument
    tenant_id = sys.argv[1] if len(sys.argv) > 1 else None
    
    try:
        results = await monitor.run_comprehensive_test(tenant_id)
        
        # Print summary
        logger.info("📋 Performance Test Summary:")
        logger.info("=" * 50)
        
        # Print recommendations
        recommendations = results.get('recommendations', [])
        if recommendations:
            logger.info("💡 Recommendations:")
            for i, rec in enumerate(recommendations, 1):
                logger.info(f"   {i}. [{rec['priority']}] {rec['issue']}")
                logger.info(f"      Solution: {rec['solution']}")
                logger.info(f"      Impact: {rec['impact']}")
        else:
            logger.info("🎉 No performance issues detected!")
        
        # Save detailed results to file
        output_file = f"dashboard_performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"📄 Detailed report saved to: {output_file}")
        
    except Exception as e:
        logger.error(f"❌ Performance test failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
